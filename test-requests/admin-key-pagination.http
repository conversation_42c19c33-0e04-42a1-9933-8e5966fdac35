### 管理员密钥分页测试

### 1. 获取管理员密钥列表（使用分页，默认参数）
GET {{baseUrl}}/api/admin/key/list
Authorization: Bearer {{adminJwtToken}}

### 2. 获取管理员密钥列表（第1页，每页5条）
GET {{baseUrl}}/api/admin/key/list?page=1&pageSize=5
Authorization: Bearer {{adminJwtToken}}

### 3. 获取管理员密钥列表（第2页，每页5条）
GET {{baseUrl}}/api/admin/key/list?page=2&pageSize=5
Authorization: Bearer {{adminJwtToken}}

### 4. 获取管理员密钥列表（只显示有效的，分页）
GET {{baseUrl}}/api/admin/key/list?onlyActive=true&page=1&pageSize=10
Authorization: Bearer {{adminJwtToken}}

### 5. 获取管理员密钥列表（显示所有状态，分页）
GET {{baseUrl}}/api/admin/key/list?onlyActive=false&page=1&pageSize=10
Authorization: Bearer {{adminJwtToken}}

### 6. 获取管理员密钥列表（不使用分页，向后兼容）
GET {{baseUrl}}/api/admin/key/list?pagination=false
Authorization: Bearer {{adminJwtToken}}

### 7. 获取管理员密钥列表（测试边界值：页码为0，应该默认为1）
GET {{baseUrl}}/api/admin/key/list?page=0&pageSize=5
Authorization: Bearer {{adminJwtToken}}

### 8. 获取管理员密钥列表（测试边界值：每页大小超过100，应该默认为10）
GET {{baseUrl}}/api/admin/key/list?page=1&pageSize=200
Authorization: Bearer {{adminJwtToken}}

### 9. 获取管理员密钥列表（测试边界值：每页大小为0，应该默认为10）
GET {{baseUrl}}/api/admin/key/list?page=1&pageSize=0
Authorization: Bearer {{adminJwtToken}}

### 10. 获取管理员密钥列表（测试无效参数，应该使用默认值）
GET {{baseUrl}}/api/admin/key/list?page=abc&pageSize=xyz
Authorization: Bearer {{adminJwtToken}}
